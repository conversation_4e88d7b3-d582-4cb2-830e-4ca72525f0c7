2025-06-03 13:30:32.3569 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-06-03 13:30:32.5404 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-06-03 13:30:32.5421 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-06-03 13:30:32.5421 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-06-03 13:30:32.5421 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-06-03 13:30:32.5421 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-06-03 13:30:32.5421 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-06-03 13:30:32.5421 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-06-03 13:30:32.5446 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-06-03 13:30:32.5454 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6232 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6232 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6232 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6232 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6232 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:32.6246 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:30:54.5020 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:31:23.8008 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-06-03 13:31:32.6278 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-06-03 13:31:32.6321 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:31:32.6412 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-06-03 13:31:32.6451 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-06-03 13:31:55.7060 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:32:22.2871 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-06-03 13:32:32.6191 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-06-03 13:32:32.6200 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:32:32.6203 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-06-03 13:32:32.6203 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-06-03 13:32:58.5162 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:33:25.8717 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-06-03 13:33:32.6191 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-06-03 13:33:32.6191 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:33:32.6201 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-06-03 13:33:32.6209 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-06-03 13:33:59.5855 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:34:23.7623 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-06-03 13:34:32.6186 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-06-03 13:34:32.6186 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-06-03 13:34:32.6186 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-06-03 13:34:32.6220 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-06-03 13:34:57.0382 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
