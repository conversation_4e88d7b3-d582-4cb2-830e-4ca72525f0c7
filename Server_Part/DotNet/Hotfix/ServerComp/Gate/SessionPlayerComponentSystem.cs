﻿namespace MaoYouJi
{
  [EntitySystemOf(typeof(SessionPlayerComponent))]
  public static partial class SessionPlayerComponentSystem
  {
    [EntitySystem]
    private static void Destroy(this SessionPlayerComponent self)
    {
      Scene root = self.Root();
      if (root.IsDisposed)
      {
        return;
      }
      if (self.PlayerId == 0)
      {
        return;
      }
      // 发送断线消息
      User user = GlobalInfoCache.Instance.GetOnlineUser(self.PlayerId);
      if (user == null)
      {
        // 说明用户已经下线了
        ETLog.Error($"用户不存在: {self.PlayerId}");
        return;
      }
      ETLog.Info($"用户断线: {self.PlayerId}");
      UserActorComponent userActorComponent = user.GetComponent<UserActorComponent>();
      userActorComponent.SetSessionActorId(default);
      // 发送断线消息
      MapNode mapNode = user.GetParent<MapNode>();
      MessageQueue.Instance.Send(mapNode.GetActorId(), new InnerUserDisconnectMsg()
      {
        UserId = user.Id
      });
    }

    [EntitySystem]
    private static void Awake(this SessionPlayerComponent self)
    {

    }
  }
}