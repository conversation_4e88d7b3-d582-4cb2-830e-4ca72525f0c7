﻿namespace MaoYouJi
{
  [EntitySystemOf(typeof(SessionPlayerComponent))]
  public static partial class SessionPlayerComponentSystem
  {
    [EntitySystem]
    private static void Destroy(this SessionPlayerComponent self)
    {
      Scene root = self.Root();
      if (root.IsDisposed)
      {
        return;
      }
      // 发送断线消息
      User user = GlobalInfoCache.Instance.GetOnlineUser(self.Player.Id);
      if (user == null)
      {
        // 说明用户已经下线了
        ETLog.Error($"用户不存在: {self.Player.Id}");
        return;
      }
      ETLog.Info($"用户断线: {self.Player.Id}");
      // 发送断线消息
      MapNode mapNode = user.GetParent<MapNode>();
      MessageQueue.Instance.Send(mapNode.GetActorId(), new InnerUserDisconnectMsg()
      {
        UserId = user.Id
      });
    }

    [EntitySystem]
    private static void Awake(this SessionPlayerComponent self)
    {

    }
  }
}