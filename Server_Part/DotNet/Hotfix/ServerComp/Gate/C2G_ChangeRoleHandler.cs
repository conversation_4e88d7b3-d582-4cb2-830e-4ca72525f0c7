using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageSessionHandler(SceneType.Gate)]
  [FriendOf(typeof(Account))]
  public class C2G_ChangeRoleHandler : MessageSessionHandler<ChangeRoleReq, ChangeRoleResp>
  {
    protected override async ETTask Run(Session session, ChangeRoleReq request, ChangeRoleResp response)
    {
      try
      {
        Scene root = session.Root();
        long userId = session.GetComponent<SessionPlayerComponent>().PlayerId;
        User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        long netAccountId = user.netAccountId;

        ETLog.Info($"用户请求更换角色: netAccountId={netAccountId}");

        // 验证账户是否存在
        GateAccountsComponent gateAccountsComponent = root.GetComponent<GateAccountsComponent>();
        Account account = await gateAccountsComponent.GetAccount(netAccountId);
        if (account == null)
        {
          ETLog.Error($"账户不存在: {netAccountId}");
          response.Error = ErrorCore.ERR_Show_Msg;
          response.showMessage = "账户不存在，请重新登录";
          return;
        }

        // 获取当前用户的Player组件（如果存在）
        PlayerComponent playerComponent = root.GetComponent<PlayerComponent>();
        playerComponent.RemoveChild(userId);

        // 获取账户下的所有角色列表
        List<AccountRoleInfo> accountRoleInfoList = await account.GetRoleInfoListEmbedded();
        response.accountRoles = accountRoleInfoList;

        ETLog.Info($"更换角色成功，返回角色列表: count={accountRoleInfoList?.Count ?? 0}");
        session.RemoveComponent<SessionPlayerComponent>();
      }
      catch (Exception ex)
      {
        ETLog.Error($"更换角色处理异常: {ex}");
        response.Error = ErrorCore.ERR_Show_Msg;
        response.showMessage = "服务器内部错误，请稍后重试";
      }
    }
  }
}
