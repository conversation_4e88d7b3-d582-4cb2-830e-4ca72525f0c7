namespace MaoYouJi
{
  [MessageSessionHandler(SceneType.Gate)]
  public class C2G_StartGameHandler : MessageSessionHandler<StartGameReq, StartGameResp>
  {
    protected override async ETTask Run(Session session, StartGameReq request, StartGameResp response)
    {
      Account account = await session.Root().GetComponent<GateAccountsComponent>().GetAccount(request.netAccountId);
      if (account == null)
      {
        ETLog.Error($"账户不存在: {request.netAccountId}");
        response.SetError("账户不存在");
        return;
      }
      if (account.accessKey != request.accessKey)
      {
        response.SetError("密钥不正确，请重新登录");
        return;
      }
      long userId = request.userId;
      if (!account.roleList.Contains(userId))
      {
        ETLog.Error($"用户不存在: {userId}, {request.netAccountId}");
        response.SetError("用户不存在");
        return;
      }
      User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      PlayerComponent playerComponent = session.Root().GetComponent<PlayerComponent>();
      FiberUsersComponent fiberUsersComponent = session.Root().GetComponent<FiberUsersComponent>();
      if (user != null)
      {
        Player prePlayer = playerComponent.GetChild<Player>(user.Id);
        if (prePlayer != null)
        {
          Session preSession = prePlayer.GetComponent<PlayerSessionComponent>()?.Session;
          if (preSession != null)
          {
            ETLog.Info($"offline preUser: {user.Id}");
            preSession.Send(new ServerReLogInMsg());
            playerComponent.RemoveChild(user.Id);
            // 如果用户的Session不是当前的，3秒后关闭Session
            if (preSession.Id != session.Id)
            {
              preSession.CloseSession(3000).Coroutine();
            }
          }
        }
      }
      else
      {
        user = await fiberUsersComponent.GetUserFromDb(userId);
        if (user == null)
        {
          ETLog.Error($"用户不存在: {userId}, {request.netAccountId}");
          response.SetError("用户不存在");
          return;
        }
        fiberUsersComponent.AddChild(user);
        fiberUsersComponent.AddUser(user, true);
        EntitySystemSingleton.Instance.Load(user);
        user.AddComponent<UserActorComponent>();
        user.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      }
      // 将用户放入地图组件之中
      ETLog.Info($"用户上线: {user.Id}, {user.nickname}, {user.netAccountId}");
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      MapNode mapNode = GlobalInfoCache.Instance.GetMapNode(moveComponent.nowMap, moveComponent.nowPoint);
      mapNode.PutUserInMapNode(user);
      UserActorComponent userActorComponent = user.GetComponent<UserActorComponent>();
      // 设置用户key和session
      long accessKey = RandomGenerator.RandInt64();
      user.accessKey = accessKey;
      Player player = playerComponent.AddChildWithId<Player, long>(user.Id, user.Id);
      PlayerSessionComponent playerSessionComponent = player.AddComponent<PlayerSessionComponent>();
      playerSessionComponent.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.GateSession);
      playerSessionComponent.Session = session;
      userActorComponent.SetSessionActorId(playerSessionComponent.GetActorId());
      session.RemoveComponent<SessionPlayerComponent>();
      session.AddComponent<SessionPlayerComponent>().PlayerId = user.Id;
      // 返回用户具体数据
      response.accessKey = accessKey;
      response.userBaseDaoInfo = user.GetUserBaseDaoInfo();
      response.bagDaoInfo = user.GetComponent<BagComponent>().GetBagDaoInfo();
      response.taskListDaoInfo = user.GetComponent<TaskComponent>().GetTaskListDaoInfo();
      response.attackDaoInfo = user.GetComponent<AttackComponent>().GetAttackDaoInfo();
      response.mapDaoInfo = mapNode.GetMapDaoInfo();
      FightInfo fightTarget = user.GetComponent<AttackComponent>().GetComponent<InFightComponent>()?.attackTarget;
      if (fightTarget != null)
      {
        response.targetDaoInfo = GlobalInfoCache.Instance.GetFightAttackComponent(fightTarget)?.GetAttackDaoInfo();
      }
    }
  }
}