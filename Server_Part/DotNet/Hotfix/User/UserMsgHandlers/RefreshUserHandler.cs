namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class RefreshUserHandler : MessageLocationHandler<MapNode, RefreshUserReq, RefreshUserResp>
  {
    protected override async ETTask Run(MapNode nowMap, RefreshUserReq request, RefreshUserResp response)
    {
      // 从GlobalInfoCache获取用户数据
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }

      // 获取用户基本信息
      response.userBaseDaoInfo = user.GetUserBaseDaoInfo();

      // 获取背包信息
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (bagComponent != null)
      {
        response.bagDaoInfo = bagComponent.GetBagDaoInfo();
      }

      // 获取战斗信息
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent != null)
      {
        response.attackDaoInfo = attackComponent.GetAttackDaoInfo();

        // 如果用户当前在战斗中，获取战斗目标信息
        InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
        if (inFightComponent != null && inFightComponent.attackTarget != null)
        {
          AttackComponent targetAttackComponent = AttackProcSys.GetAttackComponent(inFightComponent.attackTarget);
          if (targetAttackComponent != null)
          {
            response.targetDaoInfo = targetAttackComponent.GetSimpleAttackDaoInfo();
          }
        }
      }

      // 获取当前地图信息
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      if (moveComponent != null)
      {
        MapNode userMapNode = GlobalInfoCache.Instance.GetMapNode(moveComponent.nowMap, moveComponent.nowPoint);
        if (userMapNode != null)
        {
          response.mapDaoInfo = userMapNode.GetMapDaoInfo();
        }
      }

      await ETTask.CompletedTask;
    }
  }
}
