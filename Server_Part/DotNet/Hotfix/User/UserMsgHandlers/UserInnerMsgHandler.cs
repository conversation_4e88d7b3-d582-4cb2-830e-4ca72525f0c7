namespace MaoYouJi
{
  [MessageHandler(SceneType.Map)]
  public class InnerUserDisconnectMsgHandler : MessageHandler<MapNode, InnerUserDisconnectMsg>
  {
    protected override async ETTask Run(MapNode nowMap, InnerUserDisconnectMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithTransfer(msg.UserId, out User user, msg);
      if (logicRet != LogicRet.Success)
      {
        return;
      }
      user.offlineState = OfflineStateEnum.NEED_ONLINE;
      nowMap.PutUserInMapNode(user);
      // 断线需要把ActorId置为默认值
      UserActorComponent userActorComponent = user.GetComponent<UserActorComponent>();
      userActorComponent.UserSessionActorId = default;
      string jobId = user.Id.ToString() + "_offline";
      ETLog.Info($"用户Map下线: {user.Id}");
      if (QuartzScheduler.Instance.HasJob(JobIdHelper.USER_GROUP, jobId))
      {
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      MaoScheduleJobInfo jobInfo = new(attackComponent.fightInfo, new InnerUserOfflineMsg { UserId = user.Id });
      QuartzScheduler.Instance.AddScheduleJob(jobInfo, JobIdHelper.USER_GROUP, jobId, 1000 * 60 * 5);
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Map)]
  public class InnerUserReconnectMsgHandler : MessageHandler<MapNode, InnerUserReconnectMsg>
  {
    protected override async ETTask Run(MapNode nowMap, InnerUserReconnectMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithTransfer(msg.UserId, out User user, msg);
      if (logicRet != LogicRet.Success)
      {
        return;
      }
      user.offlineState = OfflineStateEnum.ONLINE;
      nowMap.PutUserInMapNode(user);
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Map)]
  public class InnerUserOfflineMsgHandler : MessageHandler<MapNode, InnerUserOfflineMsg>
  {
    protected override async ETTask Run(MapNode nowMap, InnerUserOfflineMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithTransfer(msg.UserId, out User user, msg);
      if (logicRet != LogicRet.Success)
      {
        return;
      }
      if (user.offlineState != OfflineStateEnum.NEED_ONLINE)
      {
        ETLog.Warning($"用户不在需要下线状态: {user.Id}");
      }
      user.offlineState = OfflineStateEnum.OFFLINE;
      nowMap.RemoveUserInMapNode(user);
      await ETTask.CompletedTask;
    }
  }
}