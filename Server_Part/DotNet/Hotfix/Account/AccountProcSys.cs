using System.Collections.Generic;

namespace MaoYouJi
{
  public static class AccountProcSys
  {
    public static async ETTask<Account> GetAccount(this User self)
    {
      // 获取用户所在的网关协程
      StartSceneConfig startSceneConfig = RealmGateAddressHelper.GetGate(self.Zone(), self.netAccountId);
      Fiber fiber = FiberManager.Instance.Get(startSceneConfig.Id);
      if (fiber == null)
      {
        return null;
      }
      // 根据网关账户ID获取本地账号
      GateAccountsComponent gateAccountsComponent = fiber.Root.GetComponent<GateAccountsComponent>();
      Account account = await gateAccountsComponent.GetAccount(self.netAccountId);
      return account;
    }
  }
}