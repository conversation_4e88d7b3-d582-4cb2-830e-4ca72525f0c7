using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(TeamMember))]
  [FriendOf(typeof(TeamMember))]
  public static partial class TeamMemberSystem
  {
    [EntitySystem]
    private static void Awake(this TeamMember self, User user)
    {
      TeamInfo teamInfo = self.GetParent<TeamInfo>();
      if (teamInfo == null)
      {
        return;
      }
      teamInfo.memberInfosLock.EnterWriteLock();
      try
      {
        user.teamInfo = new UserTeamInfo
        {
          teamId = teamInfo.Id,
          teamName = teamInfo.name,
        };
        self.userId = user.Id;
        TeamMemberInfo memberInfo = new();
        memberInfo.isFollow = false;
        TeamProcSys.FillMemberInfo(memberInfo, user);
        teamInfo.memberInfos.TryAdd(user.Id, memberInfo);
        user.SendToast($"您加入队伍{teamInfo.name}");
      }
      finally
      {
        teamInfo.memberInfosLock.ExitWriteLock();
      }
      TeamManageComponent teamManageComponent = teamInfo.Scene().GetComponent<TeamManageComponent>();
      teamManageComponent.RemoveApplyInfo(0, user.Id, false);
    }

    [EntitySystem]
    private static void Destroy(this TeamMember self)
    {
      TeamInfo teamInfo = self.GetParent<TeamInfo>();
      // 从队伍中移除
      User user = GlobalInfoCache.Instance.GetOnlineUser(self.userId);
      if (user != null)
      {
        user.teamInfo = null;
      }
      // 从队伍中移除
      if (teamInfo != null && teamInfo.memberInfos != null)
      {
        teamInfo.memberInfosLock.EnterWriteLock();
        try
        {
          teamInfo.memberInfos.TryRemove(self.userId, out _);
        }
        finally
        {
          teamInfo.memberInfosLock.ExitWriteLock();
        }
      }
    }
  }

  [FriendOf(typeof(TeamInfo))]
  public static partial class TeamInfoSystem
  {
    [EntitySystem]
    private static void Awake(this TeamInfo self, ClientCreateTeamMsg msg, User user)
    {
      self.updateTime = TimeInfo.Instance.ServerNow();
      self.name = msg.teamName;
      self.description = msg.teamDesc;
      self.isOpen = msg.isOpen;
      self.leaderId = user.Id;
      self.filterInfo = msg.filterInfo;
      if (self.filterInfo == null)
      {
        self.filterInfo = new TeamFilterInfo
        {
          minAttack = 0,
          minLevel = 0,
          maxLevel = 99
        };
      }
      if (msg.teamType != TeamType.None)
      {
        self.teamType = msg.teamType;
      }
      else
      {
        self.teamType = TeamType.Other_Team;
      }
      self.AddChildWithId<TeamMember, User>(user.Id, user);
      self.leaderId = user.Id;
      self.SaveTeamInfo(true).Coroutine();
      GlobalInfoCache.Instance.allTeamCache.TryAdd(self.Id, self);
      user.teamInfo = new UserTeamInfo
      {
        teamId = self.Id,
        teamName = self.name
      };
    }

    [EntitySystem]
    private static void Destroy(this TeamInfo self)
    {
      // 从全局缓存中移除
      GlobalInfoCache.Instance.allTeamCache.TryRemove(self.Id, out _);
      var dbComponent = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamInfo>();
      dbComponent.DeleteOne(x => x.Id == self.Id);
      TeamManageComponent teamManageComponent = self.Scene().GetComponent<TeamManageComponent>();
      teamManageComponent.RemoveApplyInfo(self.Id, 0, false);
      teamManageComponent.RemoveApplyInfo(self.Id, 0, true);
    }

    // 检查是否可以加入队伍
    public static LogicRet CanApply(this TeamInfo self, User user)
    {
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (self.filterInfo != null)
      {
        if (attackComponent.level < self.filterInfo.minLevel
            || attackComponent.level > self.filterInfo.maxLevel)
        {
          return LogicRet.Failed("等级不符合要求");
        }
        if (attackComponent.attackNum < self.filterInfo.minAttack)
        {
          return LogicRet.Failed("战斗力不符合要求");
        }
      }
      return LogicRet.Success;
    }

    public static bool IsTeamMember(this TeamInfo self, long userId)
    {
      return self.memberInfos.ContainsKey(userId);
    }

    // 是否是队长
    public static bool IsLeader(this TeamInfo self, long userId)
    {
      return self.leaderId == userId;
    }

    // 获取队伍成员信息
    public static List<TeamMemberInfo> GetMemberInfos(this TeamInfo self)
    {
      return self.memberInfos.Values.ToList();
    }

    public static bool HasSameAccount(this TeamInfo self, User user)
    {
      foreach (var userId in self.memberInfos.Keys)
      {
        User userCacheInfo = GlobalInfoCache.Instance.GetOnlineUser(userId);
        if (userCacheInfo != null && userCacheInfo.netAccountId == user.netAccountId)
        {
          return true;
        }
      }
      return false;
    }

    // 刷新队伍成员信息
    public static void RefreshMemberInfo(this TeamInfo self)
    {
      self.memberInfosLock.EnterReadLock();
      try
      {
        foreach (var memberInfo in self.memberInfos.Values)
        {
          User user = GlobalInfoCache.Instance.GetOnlineUser(memberInfo.userId);
          if (user != null)
          {
            TeamProcSys.FillMemberInfo(memberInfo, user);
          }
        }
        self.updateTime = TimeInfo.Instance.ServerNow();
      }
      finally
      {
        self.memberInfosLock.ExitReadLock();
      }
    }

    // 编辑队伍信息
    public static void EditTeam(this TeamInfo self, ClientEditTeamMsg msg, User leaderInfo)
    {
      if (msg.teamName != null)
      {
        self.name = msg.teamName;
      }
      if (msg.teamDesc != null)
      {
        self.description = msg.teamDesc;
      }
      if (msg.teamType != TeamType.None)
      {
        self.teamType = msg.teamType;
      }
      AttackComponent attackComponent = leaderInfo.GetComponent<AttackComponent>();
      if (msg.minAttackNum != null)
      {
        if (attackComponent.attackNum < msg.minAttackNum)
        {
          leaderInfo.SendToast("战斗力筛选不能大于队长的战斗力");
          return;
        }
        self.filterInfo.minAttack = (int)msg.minAttackNum;
      }
      if (msg.minLevel != null)
      {
        if (attackComponent.level < msg.minLevel)
        {
          leaderInfo.SendToast("等级筛选不能大于队长的等级");
          return;
        }
        if (msg.minLevel > self.filterInfo.maxLevel)
        {
          leaderInfo.SendToast("最小等级不能大于最大等级");
          return;
        }
        self.filterInfo.minLevel = (int)msg.minLevel;
      }
      if (msg.maxLevel != null)
      {
        if (attackComponent.level > msg.maxLevel)
        {
          leaderInfo.SendToast("最大等级不能小于队长的等级");
          return;
        }
        if (msg.maxLevel < self.filterInfo.minLevel)
        {
          leaderInfo.SendToast("最大等级不能小于最小等级");
          return;
        }
        self.filterInfo.maxLevel = (int)msg.maxLevel;
      }
      if (msg.isOpen != null)
      {
        self.isOpen = (bool)msg.isOpen;
      }
      self.updateTime = TimeInfo.Instance.ServerNow();
      self.SaveTeamInfo().Coroutine();
    }

    public static async ETTask SaveTeamInfo(this TeamInfo self, bool isInsert = false)
    {
      var dbComponent = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<TeamInfo>();
      if (isInsert)
      {
        await dbComponent.InsertOneAsync(self);
      }
      else
      {
        await dbComponent.ReplaceOneAsync(x => x.Id == self.Id, self, new ReplaceOptions { IsUpsert = true });
      }
    }

    public static bool FollowLeader(this TeamInfo self, User user)
    {
      if (self.leaderId == user.Id)
      {
        return false;
      }
      TeamMemberInfo memberInfo = self.memberInfos[user.Id];
      if (memberInfo == null)
      {
        return false;
      }
      memberInfo.isFollow = !memberInfo.isFollow;
      self.updateTime = TimeInfo.Instance.ServerNow();
      return memberInfo.isFollow;
    }

    public static void SendMessageToAllMember(this TeamInfo self, params MaoYouMessage[] messages)
    {
      foreach (var memberInfo in self.memberInfos.Values)
      {
        User user = GlobalInfoCache.Instance.GetOnlineUser(memberInfo.userId);
        if (user != null)
        {
          user.SendMessage(messages);
        }
      }
    }
  }
}