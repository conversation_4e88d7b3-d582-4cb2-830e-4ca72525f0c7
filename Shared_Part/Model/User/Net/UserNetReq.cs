using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AccountLogInReq)]
  [ResponseType(nameof(AccountLogInResp))]
  public partial class AccountLogInReq : MaoYouInMessage, ISessionRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.TapTapLogInReq)]
  [ResponseType(nameof(TapTapLogInResp))]
  public partial class TapTapLogInReq : MaoYouInMessage, ISessionRequest
  {
    public int OwnerFiberId;
    public TapTapAccessToken tapTapAccessToken;
    public bool isReconnet = false;
    public long accountId;
    public long accessKey;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.SelectServerReq)]
  [ResponseType(nameof(SelectServerResp))]
  public partial class SelectServerReq : MaoYouInMessage, ISessionRequest
  {
    public int zoneId;
    public long netAccountId;
    public long accessKey;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GateLogInReq)]
  [ResponseType(nameof(GateLogInResp))]
  public partial class GateLogInReq : MaoYouInMessage, ISessionRequest
  {
    public long Key;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateRoleReq)]
  [ResponseType(nameof(CreateRoleResp))]
  public partial class CreateRoleReq : MaoYouInMessage, ISessionRequest
  {
    public string nickname;
    public BaseJob job;
    public SkinIdEnum defaultSkin;
    public long accountId;
    public long accessKey;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.CheckNicknameReq)]
  [ResponseType(nameof(CheckNicknameResp))]
  public partial class CheckNicknameReq : MaoYouInMessage, ISessionRequest
  {
    public string nickname;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.StartGameReq)]
  [ResponseType(nameof(StartGameResp))]
  public partial class StartGameReq : MaoYouInMessage, ISessionRequest
  {
    public long userId;
    public long netAccountId;
    public long accessKey;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ChangeSkinReq)]
  [ResponseType(nameof(ChangeSkinResp))]
  public partial class ChangeSkinReq : MaoYouInMessage, ILocationRequest
  {
    public SkinIdEnum skinId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.RefreshUserReq)]
  [ResponseType(nameof(RefreshUserResp))]
  public partial class RefreshUserReq : MaoYouInMessage, ILocationRequest
  {
  }
}