using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.AccountLogInResp)]
  public partial class AccountLogInResp : MaoYouOutMessage, ISessionResponse
  {
    public long accountId;
    public string accessKey;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.TapTapLogInResp)]
  public partial class TapTapLogInResp : MaoYouOutMessage, ISessionResponse
  {
    // 以下为taptap登录账户信息
    public string name;
    public string avatar;
    public string openId;
    public string unionId;
    // 账号ID
    public long netAccountId;
    // 登录凭证
    public long accessKey;
    public List<ServerData> serverList;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.SelectServerResp)]
  public partial class SelectServerResp : MaoYouOutMessage, ISessionResponse
  {
    public string Address;
    public long Key;
    public long GateId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.GateLogInResp)]
  public partial class GateLogInResp : MaoYouOutMessage, ISessionResponse
  {
    public long accessKey;
    public List<AccountRoleInfo> accountRoles;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.CreateRoleResp)]
  public partial class CreateRoleResp : MaoYouOutMessage, ISessionResponse
  {
    public List<AccountRoleInfo> accountRoles;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.CheckNicknameResp)]
  public partial class CheckNicknameResp : MaoYouOutMessage, ISessionResponse
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.StartGameResp)]
  public partial class StartGameResp : MaoYouOutMessage, ISessionResponse
  {
    public long accessKey;
    public UserBaseDaoInfo userBaseDaoInfo;
    public BagDaoInfo bagDaoInfo;
    public TaskListDaoInfo taskListDaoInfo;
    public AttackDaoInfo attackDaoInfo;
    public MapDaoInfo mapDaoInfo;
    public AttackDaoInfo targetDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ChangeSkinResp)]
  public partial class ChangeSkinResp : MaoYouOutMessage, ILocationResponse
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.RefreshUserResp)]
  public partial class RefreshUserResp : MaoYouOutMessage, ILocationResponse
  {
    public UserBaseDaoInfo userBaseDaoInfo;
    public BagDaoInfo bagDaoInfo;
    public AttackDaoInfo attackDaoInfo;
    public MapDaoInfo mapDaoInfo;
    public AttackDaoInfo targetDaoInfo; // 战斗目标信息，如果用户当前在战斗中
  }
}