namespace MaoYouJi
{
  // 用户消息 40001 - 42000
  [EnableClass]
  public partial class MaoOuterMessageRange
  {
    public const ushort AccountLogInReq = 40001;
    public const ushort AccountLogInResp = 40002;
    public const ushort TapTapLogInReq = 40003;
    public const ushort TapTapLogInResp = 40004;
    public const ushort AccountLogOutReq = 40005;
    public const ushort AccountLogOutResp = 40006;
    public const ushort TapTapLogOutReq = 40007;
    public const ushort TapTapLogOutResp = 40008;
    public const ushort SelectServerReq = 40009; // 选择服务器
    public const ushort SelectServerResp = 40010; // 选择服务器
    public const ushort GateLogInReq = 40011; // 登录网关
    public const ushort GateLogInResp = 40012; // 登录网关
    public const ushort GateLogOutReq = 40013; // 登出网关
    public const ushort GateLogOutResp = 40014; // 登出网关
    public const ushort CreateRoleReq = 40015; // 创建角色
    public const ushort CreateRoleResp = 40016; // 创建角色
    public const ushort CheckNicknameReq = 40017; // 检查昵称
    public const ushort CheckNicknameResp = 40018; // 检查昵称
    public const ushort StartGameReq = 40019; // 开始游戏
    public const ushort StartGameResp = 40020; // 开始游戏
    public const ushort ServerReLogInMsg = 40021; // 需要重新登录
    public const ushort ClientShowUserMsg = 40022; // 客户端显示用户信息
    public const ushort ServerShowUserMsg = 40023; // 服务器显示用户信息
    public const ushort ServerUpdatePartUserInfo = 40024; // 服务器更新部分用户信息
    public const ushort ClientReviveUserMsg = 40025; // 客户端复活用户
    public const ushort ChangeSkinReq = 40026; // 更换皮肤
    public const ushort ChangeSkinResp = 40027; // 更换皮肤
    public const ushort RefreshUserReq = 40028; // 刷新用户状态请求 
    public const ushort RefreshUserResp = 40029; // 刷新用户状态响应
    public const ushort ChangeRoleReq = 40030; // 更换角色请求
    public const ushort ChangeRoleResp = 40031; // 更换角色响应
  }
}