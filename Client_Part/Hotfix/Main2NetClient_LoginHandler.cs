﻿using System;
using System.Net;
using System.Net.Sockets;

namespace MaoYouJi
{
  [MessageHandler(SceneType.NetClient)]
  public class Main2NetClient_LoginHandler : MessageHandler<Scene, TapTapLogInReq, TapTapLogInResp>
  {
    protected override async ETTask Run(Scene root, TapTapLogInReq request, TapTapLogInResp response)
    {
      // 创建一个ETModel层的Session
      root.RemoveComponent<RouterAddressComponent>();
      // 获取路由跟realm Dispatcher地址
      RouterAddressComponent routerAddressComponent =
              root.AddComponent<RouterAddressComponent, string, int>(ConstValue.RouterHttpHost, ConstValue.RouterHttpPort);
      await routerAddressComponent.Init();
      root.AddComponent<NetComponent, AddressFamily, NetworkProtocol>(routerAddressComponent.RouterManagerIPAddress.AddressFamily, NetworkProtocol.TCP);
      root.GetComponent<FiberParentComponent>().ParentFiberId = request.OwnerFiberId;

      NetComponent netComponent = root.GetComponent<NetComponent>();

      IPEndPoint realmAddress = routerAddressComponent.GetRealmAddress();

      TapTapAccessToken tapTapAccessToken = request.tapTapAccessToken;

      TapTapLogInResp tapTapLogInOut;
      Session realmSession = await netComponent.CreateRouterSession(realmAddress, tapTapAccessToken.openId);

      ETLog.Debug($"c2rLogin");
      TapTapLogInReq tapTapLogInIn = new()
      {
        tapTapAccessToken = tapTapAccessToken
      };
      tapTapLogInOut = (TapTapLogInResp)await realmSession.Call(tapTapLogInIn);

      if (tapTapLogInOut.Error != 0)
      {
        ETLog.Error($"c2rLogin error: {tapTapLogInOut.Error}");
        return;
      }
      response.serverList = tapTapLogInOut.serverList;
      response.name = tapTapLogInOut.name;
      response.avatar = tapTapLogInOut.avatar;
      response.openId = tapTapLogInOut.openId;
      response.unionId = tapTapLogInOut.unionId;
      response.netAccountId = tapTapLogInOut.netAccountId;
      response.accessKey = tapTapLogInOut.accessKey;

      ETLog.Debug($"r2cLogin: {tapTapLogInOut}");

      realmSession.AddComponent<ClientSessionErrorComponent>();

      root.RemoveComponent<SessionComponent>();
      root.AddComponent<SessionComponent>().Session = realmSession;

      ETLog.Debug("登陆gate成功!");
    }
  }

  [MessageHandler(SceneType.NetClient)]
  public class Main2NetClient_SelectServerHandler : MessageHandler<Scene, SelectServerReq, SelectServerResp>
  {
    protected override async ETTask Run(Scene root, SelectServerReq request, SelectServerResp response)
    {
      Session realmSession = root.GetComponent<SessionComponent>().Session;

      NetComponent netComponent = root.GetComponent<NetComponent>();

      SelectServerReq requestLoginGate = new()
      {
        zoneId = request.zoneId,
        netAccountId = request.netAccountId,
        accessKey = request.accessKey
      };
      SelectServerResp responseLoginGate = (SelectServerResp)await realmSession.Call(requestLoginGate);
      response.Address = responseLoginGate.Address;
      response.Key = responseLoginGate.Key;
      response.GateId = responseLoginGate.GateId;

      // 创建一个gate Session,并且保存到SessionComponent中
      Session gateSession = await netComponent.CreateGateSession(NetworkHelper.ToIPEndPoint(responseLoginGate.Address), request.netAccountId.ToString());
      gateSession.AddComponent<ClientSessionErrorComponent>();
      ETLog.Debug("登陆gate成功!");
      // 销毁realmSession
      realmSession.Dispose();
      root.RemoveComponent<SessionComponent>();
      root.AddComponent<SessionComponent>().Session = gateSession;

      // response.PlayerId = g2CLoginGate.PlayerId;
    }
  }

  [MessageHandler(SceneType.NetClient)]
  public class Main2NetClient_GateLogInHandler : MessageHandler<Scene, GateLogInReq, GateLogInResp>
  {
    protected override async ETTask Run(Scene root, GateLogInReq request, GateLogInResp response)
    {
      Session gateSession = root.GetComponent<SessionComponent>().Session;

      GateLogInResp responseLoginGate = (GateLogInResp)await gateSession.Call(request);
      response.accessKey = responseLoginGate.accessKey;
      response.accountRoles = responseLoginGate.accountRoles;
    }
  }

}